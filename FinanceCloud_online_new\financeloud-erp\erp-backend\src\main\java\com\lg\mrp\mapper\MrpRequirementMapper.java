package com.lg.mrp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lg.financecloud.common.data.datascope.CloudxBaseMapper;
import com.lg.mrp.dto.MrpRequirementQueryRequest;
import com.lg.mrp.entity.MrpRequirement;
import com.lg.mrp.vo.MrpRequirementVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * MRP需求Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MrpRequirementMapper extends CloudxBaseMapper<MrpRequirement> {

    /**
     * 查询计划中的不重复物料ID列表
     */
    @Select("SELECT DISTINCT material_id FROM mrp_requirement WHERE plan_id = #{planId} AND tenant_id = #{tenantId}")
    List<String> selectDistinctMaterialIds(@Param("planId") String planId, @Param("tenantId") Integer tenantId);

    /**
     * 分页查询需求列表(关联计划表)
     */
    Page<MrpRequirementVO> selectRequirementPageList(Page<MrpRequirementVO> page,
                                                   @Param("tenantId") Integer tenantId, 
                                                   @Param("request") MrpRequirementQueryRequest request);
}