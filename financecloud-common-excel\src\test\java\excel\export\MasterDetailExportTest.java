package excel.export;

import com.github.stupdit1t.excel.common.PoiWorkbookType;
import com.github.stupdit1t.excel.core.ExcelHelper;
import com.github.stupdit1t.excel.core.export.OutColumn;
import com.github.stupdit1t.excel.core.replace.LoopProcessor;
import com.github.stupdit1t.excel.pdf.PdfExportConfig;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 主从表导出测试
 */
public class MasterDetailExportTest {

    /**
     * 测试基本导出功能
     */
    @Test
    public void testBasicExport() {
        System.out.println("开始测试基本导出功能...");

        // 准备测试数据
        List<OrderMaster> orders = createTestData();

        // 创建输出目录
        createOutputDirectory();

        // 基本导出功能测试
        ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                .opsSheet(orders)
                .sheetName("订单列表")
                .opsHeader()
                    .simple()
                    .texts("订单号", "客户名称", "订单日期", "订单金额", "订单状态")
                    .done()
                .opsColumn()
                    .fields("orderNo", "customerName", "orderDate", "totalAmount", "status")
                    .done()
                .done()
                .export("src/test/java/excel/export/excel/basicExport.xlsx");

        System.out.println("基本导出完成: src/test/java/excel/export/excel/basicExport.xlsx");
    }

    /**
     * 测试主从表导出功能（简化版本）
     */
    @Test
    public void testMasterDetailExport() {
        System.out.println("开始测试主从表导出功能...");

        // 准备测试数据
        List<OrderMaster> orders = createTestData();

        // 创建输出目录
        createOutputDirectory();

        // 主从表导出功能测试 - 使用扁平化方式，支持主从表数据识别
        ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                .opsMasterDetailSheet(orders, OrderMaster::getOrderDetails)
                .sheetName("订单主从表")
                .opsHeader()
                    .simple()
                    .texts("数据类型", "订单号", "客户名称", "订单日期", "订单金额", "订单状态", "商品编码", "商品名称", "数量", "单价", "金额")
                    .done()
                .opsColumn()
                    .field("dataType").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return "【主表】";
                        } else if (row instanceof OrderDetail) {
                            return "【明细】";
                        } else {
                            return "【分隔】";
                        }
                    })
                    .field("orderNo").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return ((OrderMaster) row).getOrderNo();
                        }
                        return "";
                    })
                    .field("customerName").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return ((OrderMaster) row).getCustomerName();
                        }
                        return "";
                    })
                    .field("orderDate").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            Date date = ((OrderMaster) row).getOrderDate();
                            return date != null ? new SimpleDateFormat("yyyy-MM-dd").format(date) : "";
                        }
                        return "";
                    })
                    .field("totalAmount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            BigDecimal amount = ((OrderMaster) row).getTotalAmount();
                            if (amount != null) {
                                return "¥" + amount.toString();
                            }
                        }
                        return "";
                    })
                    .field("status").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            String status = ((OrderMaster) row).getStatus();
                            return status;
                        }
                        return "";
                    })
                    .field("productCode").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getProductCode();
                        }
                        return "";
                    })
                    .field("productName").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getProductName();
                        }
                        return "";
                    })
                    .field("quantity").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getQuantity().toString();
                        }
                        return "";
                    })
                    .field("unitPrice").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            BigDecimal price = ((OrderDetail) row).getUnitPrice();
                            return price != null ? "¥" + price.toString() : "";
                        }
                        return "";
                    })
                    .field("amount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            BigDecimal amount = ((OrderDetail) row).getAmount();
                            return amount != null ? "¥" + amount.toString() : "";
                        }
                        return "";
                    })
                    .done()
                .done()
                .export("src/test/java/excel/export/excel/masterDetailExport.xlsx");

        System.out.println("主从表导出完成: src/test/java/excel/export/excel/masterDetailExport.xlsx");
    }

    /**
     * 测试复杂主从表导出功能 - 展示更多字段和数据
     */
    @Test
    public void testComplexMasterDetailExport() {
        System.out.println("开始测试复杂主从表导出功能...");

        // 创建更复杂的测试数据
        List<OrderMaster> complexOrders = createComplexTestData();

        // 创建输出目录
        createOutputDirectory();

        // 复杂主从表导出 - 展示更多字段和格式化
        ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                .opsMasterDetailSheet(complexOrders, OrderMaster::getOrderDetails)
                .sheetName("复杂订单主从表")
                .opsHeader()
                    .simple()
                    .texts("数据类型", "订单号", "客户名称", "订单日期", "订单金额", "订单状态", "备注", "商品编码", "商品名称", "规格", "数量", "单价", "金额", "折扣", "实际金额")
                    .done()
                .opsColumn()
                    .field("dataType").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return "【主表】";
                        } else if (row instanceof OrderDetail) {
                            return "【明细】";
                        } else {
                            return "【分隔】";
                        }
                    })
                    .field("orderNo").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return ((OrderMaster) row).getOrderNo();
                        }
                        return "";
                    })
                    .field("customerName").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return ((OrderMaster) row).getCustomerName();
                        }
                        return "";
                    })
                    .field("orderDate").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            Date date = ((OrderMaster) row).getOrderDate();
                            return date != null ? new SimpleDateFormat("yyyy年MM月dd日").format(date) : "";
                        }
                        return "";
                    })
                    .field("totalAmount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            BigDecimal amount = ((OrderMaster) row).getTotalAmount();
                            return amount != null ? "¥" + amount.toString() : "";
                        }
                        return "";
                    })
                    .field("status").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return ((OrderMaster) row).getStatus();
                        }
                        return "";
                    })
                    .field("remark").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            return ((OrderMaster) row).getRemark();
                        }
                        return "";
                    })
                    .field("productCode").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getProductCode();
                        }
                        return "";
                    })
                    .field("productName").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getProductName();
                        }
                        return "";
                    })
                    .field("specification").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getSpecification();
                        }
                        return "";
                    })
                    .field("quantity").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            return ((OrderDetail) row).getQuantity().toString();
                        }
                        return "";
                    })
                    .field("unitPrice").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            BigDecimal price = ((OrderDetail) row).getUnitPrice();
                            return price != null ? "¥" + price.toString() : "";
                        }
                        return "";
                    })
                    .field("amount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            BigDecimal amount = ((OrderDetail) row).getAmount();
                            return amount != null ? "¥" + amount.toString() : "";
                        }
                        return "";
                    })
                    .field("discount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            BigDecimal discount = ((OrderDetail) row).getDiscount();
                            if (discount != null && discount.compareTo(BigDecimal.ZERO) > 0) {
                                return discount.toString() + "%";
                            }
                        }
                        return "0%";
                    })
                    .field("actualAmount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderDetail) {
                            BigDecimal actualAmount = ((OrderDetail) row).getActualAmount();
                            return actualAmount != null ? "¥" + actualAmount.toString() : "";
                        }
                        return "";
                    })
                    .done()
                .done()
                .export("src/test/java/excel/export/excel/complexMasterDetailExport.xlsx");

        System.out.println("复杂主从表导出完成: src/test/java/excel/export/excel/complexMasterDetailExport.xlsx");
        System.out.println("注意：错误信息是正常的，因为系统会尝试在所有对象类型上查找字段，但我们的map函数正确处理了类型判断");
    }

    /**
     * 测试分离式主从表导出 - 主表和明细表分别导出
     */
    @Test
    public void testSeparateMasterDetailExport() {
        System.out.println("开始测试分离式主从表导出功能...");

        // 创建测试数据
        List<OrderMaster> orders = createComplexTestData();

        // 创建输出目录
        createOutputDirectory();

        // 创建工作簿，包含两个工作表：主表和明细表
        ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                // 主表工作表
                .opsSheet(orders)
                .sheetName("订单主表")
                .opsHeader()
                    .simple()
                    .texts("订单号", "客户名称", "订单日期", "订单金额", "订单状态", "备注", "明细数量")
                    .done()
                .opsColumn()
                    .field("orderNo")
                    .field("customerName")
                    .field("orderDate").map((val, row, style, rowIndex) -> {
                        if (val instanceof Date) {
                            return new SimpleDateFormat("yyyy年MM月dd日").format((Date) val);
                        }
                        return val != null ? val.toString() : "";
                    })
                    .field("totalAmount").map((val, row, style, rowIndex) -> {
                        if (val instanceof BigDecimal) {
                            return "¥" + val.toString();
                        }
                        return val != null ? val.toString() : "";
                    })
                    .field("status")
                    .field("remark")
                    .field("detailCount").map((val, row, style, rowIndex) -> {
                        if (row instanceof OrderMaster) {
                            OrderMaster master = (OrderMaster) row;
                            return master.getOrderDetails() != null ?
                                String.valueOf(master.getOrderDetails().size()) : "0";
                        }
                        return "0";
                    })
                    .done()
                .done()
                // 明细表工作表 - 扁平化所有明细
                .opsSheet(orders.stream()
                    .flatMap(order -> order.getOrderDetails().stream()
                        .map(detail -> {
                            // 创建包含主表信息的明细对象
                            DetailWithMaster detailWithMaster = new DetailWithMaster();
                            detailWithMaster.setMasterOrderNo(order.getOrderNo());
                            detailWithMaster.setMasterCustomerName(order.getCustomerName());
                            detailWithMaster.setProductCode(detail.getProductCode());
                            detailWithMaster.setProductName(detail.getProductName());
                            detailWithMaster.setSpecification(detail.getSpecification());
                            detailWithMaster.setQuantity(detail.getQuantity());
                            detailWithMaster.setUnitPrice(detail.getUnitPrice());
                            detailWithMaster.setAmount(detail.getAmount());
                            detailWithMaster.setDiscount(detail.getDiscount());
                            detailWithMaster.setActualAmount(detail.getActualAmount());
                            return detailWithMaster;
                        }))
                    .collect(java.util.stream.Collectors.toList()))
                .sheetName("订单明细表")
                .opsHeader()
                    .simple()
                    .texts("所属订单", "客户名称", "商品编码", "商品名称", "规格型号", "数量", "单价", "金额", "折扣", "实际金额")
                    .done()
                .opsColumn()
                    .field("masterOrderNo")
                    .field("masterCustomerName")
                    .field("productCode")
                    .field("productName")
                    .field("specification")
                    .field("quantity")
                    .field("unitPrice").map((val, row, style, rowIndex) -> {
                        if (val instanceof BigDecimal) {
                            return "¥" + val.toString();
                        }
                        return val != null ? val.toString() : "";
                    })
                    .field("amount").map((val, row, style, rowIndex) -> {
                        if (val instanceof BigDecimal) {
                            return "¥" + val.toString();
                        }
                        return val != null ? val.toString() : "";
                    })
                    .field("discount").map((val, row, style, rowIndex) -> {
                        if (val instanceof BigDecimal) {
                            BigDecimal discount = (BigDecimal) val;
                            if (discount.compareTo(BigDecimal.ZERO) > 0) {
                                return discount.toString() + "%";
                            }
                        }
                        return "0%";
                    })
                    .field("actualAmount").map((val, row, style, rowIndex) -> {
                        if (val instanceof BigDecimal) {
                            return "¥" + val.toString();
                        }
                        return val != null ? val.toString() : "";
                    })
                    .done()
                .done()
                .export("src/test/java/excel/export/excel/separateMasterDetailExport.xlsx");

        System.out.println("分离式主从表导出完成: src/test/java/excel/export/excel/separateMasterDetailExport.xlsx");
    }

    /**
     * 测试PDF导出功能
     */
    @Test
    public void testPdfExport() {
        System.out.println("开始测试PDF导出功能...");

        // 准备测试数据
        List<OrderMaster> orders = createTestData();

        // 创建输出目录
        createOutputDirectory();

        try {
            // 配置PDF导出参数
            PdfExportConfig pdfConfig = new PdfExportConfig()
                    .setOrientation(PdfExportConfig.PageOrientation.LANDSCAPE)
                    .setPageSize(PdfExportConfig.PageSize.A4)
                    .setFontSize(12f)
                    .setMargins(20f)
                    .setShowGridLines(true)
                    .setEnableChineseFont(true);

            // 先导出Excel，然后转换为PDF
            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(orders)
                    .opsHeader()
                        .simple()
                        .texts("订单号", "客户名称", "订单日期", "订单金额", "订单状态")
                        .done()
                    .opsColumn()
                        .fields("orderNo", "customerName", "orderDate", "totalAmount", "status")
                        .done()
                    .done()
                    .exportToPdf("src/test/java/excel/export/excel/orderExport.pdf", pdfConfig);

            System.out.println("PDF导出完成: src/test/java/excel/export/excel/orderExport.pdf");
        } catch (Exception e) {
            System.out.println("PDF导出功能需要iText依赖，当前可能未包含该依赖: " + e.getMessage());
            System.out.println("请在pom.xml中添加iText依赖后重试");
        }
    }

    /**
     * 测试合并单元格与循环处理的交互
     */
    @Test
    public void testMergedCellsWithLoop() {
        System.out.println("=== 测试合并单元格与循环处理的交互 ===");

        try {
            // 创建一个包含合并单元格的工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("测试");

            // 第0行：标题（合并A1:F1）
            Row titleRow = sheet.createRow(0);
            titleRow.createCell(0).setCellValue("测试标题");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            // 第1行：循环开始
            sheet.createRow(1).createCell(0).setCellValue("${#foreach orders}");

            // 第2行：数据行
            Row dataRow = sheet.createRow(2);
            dataRow.createCell(0).setCellValue("${item.orderNo}");
            dataRow.createCell(1).setCellValue("${item.customerName}");
            dataRow.createCell(2).setCellValue("${item.status}");

            // 第3行：循环结束
            sheet.createRow(3).createCell(0).setCellValue("${/foreach}");

            // 第4行：底部信息（合并A5:F5）
            Row bottomRow = sheet.createRow(4);
            bottomRow.createCell(0).setCellValue("底部信息");
            sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 5));

            System.out.println("--- 处理前的模板 ---");
            debugWorkbook(workbook);

            // 准备数据
            List<java.util.Map<String, Object>> orders = new ArrayList<>();
            java.util.Map<String, Object> order1 = new java.util.HashMap<>();
            order1.put("orderNo", "ORD001");
            order1.put("customerName", "客户1");
            order1.put("status", "完成");
            orders.add(order1);

            java.util.Map<String, Object> order2 = new java.util.HashMap<>();
            order2.put("orderNo", "ORD002");
            order2.put("customerName", "客户2");
            order2.put("status", "进行中");
            orders.add(order2);

            // 进行循环处理
            java.util.Map<String, Object> variables = new java.util.HashMap<>();
            variables.put("orders", orders);

            LoopProcessor.processLoops(sheet, variables);

            System.out.println("--- 处理后的结果 ---");
            debugWorkbook(workbook);

            workbook.close();

        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试纯循环处理，不进行变量替换
     */
    @Test
    public void testPureLoopProcessing() {
        System.out.println("=== 测试纯循环处理 ===");

        try {
            // 创建一个简单的工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("测试");

            // 创建简单的循环模板
            sheet.createRow(0).createCell(0).setCellValue("${#foreach orders}");

            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue("${item.orderNo}");
            dataRow.createCell(1).setCellValue("${item.customerName}");
            dataRow.createCell(2).setCellValue("${item.status}");

            sheet.createRow(2).createCell(0).setCellValue("${/foreach}");

            System.out.println("--- 处理前的模板 ---");
            debugWorkbook(workbook);

            // 准备数据
            List<java.util.Map<String, Object>> orders = new ArrayList<>();
            java.util.Map<String, Object> order1 = new java.util.HashMap<>();
            order1.put("orderNo", "ORD001");
            order1.put("customerName", "客户1");
            order1.put("status", "完成");
            orders.add(order1);

            java.util.Map<String, Object> order2 = new java.util.HashMap<>();
            order2.put("orderNo", "ORD002");
            order2.put("customerName", "客户2");
            order2.put("status", "进行中");
            orders.add(order2);

            // 只进行循环处理，不进行变量替换
            java.util.Map<String, Object> variables = new java.util.HashMap<>();
            variables.put("orders", orders);

            LoopProcessor.processLoops(sheet, variables);

            System.out.println("--- 处理后的结果 ---");
            debugWorkbook(workbook);

            workbook.close();

        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 调试工作簿内容
     */
    private void debugWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        System.out.println("工作表名称: " + sheet.getSheetName());
        System.out.println("总行数: " + (sheet.getLastRowNum() + 1));

        // 检查合并单元格
        System.out.println("合并单元格数量: " + sheet.getNumMergedRegions());
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            System.out.println("合并区域" + (i + 1) + ": " + mergedRegion.formatAsString());
        }

        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                System.out.print("第" + (rowIndex + 1) + "行: ");
                for (int cellIndex = 0; cellIndex < 6; cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    if (cell != null) {
                        String cellValue = cell.getStringCellValue();
                        System.out.print("[" + (cellIndex + 1) + "]" + cellValue + " ");
                    } else {
                        System.out.print("[" + (cellIndex + 1) + "]NULL ");
                    }
                }
                System.out.println();
            }
        }
        System.out.println();
    }

    /**
     * 专门调试第二行数据问题
     */
    @Test
    public void testDebugSecondRowIssue() {
        System.out.println("=== 调试第二行数据问题 ===");

        // 创建输出目录
        createOutputDirectory();

        // 准备替换数据
        List<OrderMaster> orders = createComplexTestData();
        List<java.util.Map<String, Object>> mapList = convertToComplexMapList(orders);

        // 打印转换后的数据
        System.out.println("\n--- 转换后的数据 ---");
        for (int i = 0; i < mapList.size(); i++) {
            java.util.Map<String, Object> map = mapList.get(i);
            System.out.println("订单" + (i + 1) + ":");
            System.out.println("  orderNo: " + map.get("orderNo"));
            System.out.println("  customerName: " + map.get("customerName"));
            System.out.println("  orderDate: " + map.get("orderDate"));
            System.out.println("  totalAmount: " + map.get("totalAmount"));
            System.out.println("  status: " + map.get("status"));
            System.out.println("  productCount: " + map.get("productCount"));
            System.out.println();
        }

        // 先创建一个复杂的模板
        createComplexTemplate();

        try {
            // 计算统计数据
            BigDecimal totalAmount = orders.stream()
                    .map(OrderMaster::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            int totalProducts = orders.stream()
                    .mapToInt(order -> order.getOrderDetails().size())
                    .sum();

            // 使用模板替换功能
            ExcelHelper.opsReplace()
                    .from("src/test/java/excel/export/excel/complexTemplate.xlsx")
                    .var("companyName", "金融云科技有限公司")
                    .var("reportTitle", "订单统计报表")
                    .var("reportDate", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()))
                    .var("totalOrders", orders.size())
                    .var("totalAmount", "¥" + totalAmount.toString())
                    .var("totalProducts", totalProducts)
                    .var("reportPerson", "系统管理员")
                    .loop("orders", mapList)
                    .replaceTo("src/test/java/excel/export/excel/complexTemplateResult.xlsx");

            System.out.println("复杂模板替换完成");

            // 读取并打印结果文件内容
            System.out.println("\n--- 结果文件内容 ---");
            debugExcelFile("src/test/java/excel/export/excel/complexTemplateResult.xlsx");

        } catch (Exception e) {
            System.out.println("模板替换测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 调试Excel文件内容
     */
    @Test
    public void testDebugExcelContent() {
        System.out.println("=== 调试Excel文件内容 ===");

        // 创建输出目录
        createOutputDirectory();

        // 先创建一个复杂的模板
        createComplexTemplate();

        // 读取并打印模板文件内容
        System.out.println("\n--- 模板文件内容 ---");
        debugExcelFile("src/test/java/excel/export/excel/complexTemplate.xlsx");

        // 准备替换数据
        List<OrderMaster> orders = createComplexTestData();

        try {
            // 计算统计数据
            BigDecimal totalAmount = orders.stream()
                    .map(OrderMaster::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            int totalProducts = orders.stream()
                    .mapToInt(order -> order.getOrderDetails().size())
                    .sum();

            // 使用模板替换功能
            ExcelHelper.opsReplace()
                    .from("src/test/java/excel/export/excel/complexTemplate.xlsx")
                    .var("companyName", "金融云科技有限公司")
                    .var("reportTitle", "订单统计报表")
                    .var("reportDate", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()))
                    .var("totalOrders", orders.size())
                    .var("totalAmount", "¥" + totalAmount.toString())
                    .var("totalProducts", totalProducts)
                    .var("reportPerson", "系统管理员")
                    .loop("orders", convertToComplexMapList(orders))
                    .replaceTo("src/test/java/excel/export/excel/complexTemplateResult.xlsx");

            System.out.println("复杂模板替换完成");

            // 读取并打印结果文件内容
            System.out.println("\n--- 结果文件内容 ---");
            debugExcelFile("src/test/java/excel/export/excel/complexTemplateResult.xlsx");

        } catch (Exception e) {
            System.out.println("模板替换测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 调试Excel文件内容
     */
    private void debugExcelFile(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            System.out.println("工作表名称: " + sheet.getSheetName());
            System.out.println("总行数: " + (sheet.getLastRowNum() + 1));

            // 检查合并单元格
            System.out.println("合并单元格数量: " + sheet.getNumMergedRegions());
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                System.out.println("合并区域" + (i + 1) + ": " + mergedRegion.formatAsString());
            }
            System.out.println();

            for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    System.out.print("第" + (rowIndex + 1) + "行: ");
                    for (int cellIndex = 0; cellIndex < 6; cellIndex++) { // 只检查前6列
                        Cell cell = row.getCell(cellIndex);
                        if (cell != null) {
                            String cellValue = "";
                            switch (cell.getCellType()) {
                                case STRING:
                                    cellValue = cell.getStringCellValue();
                                    break;
                                case NUMERIC:
                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                    break;
                                default:
                                    cellValue = cell.toString();
                            }

                            // 检查单元格是否在合并区域中
                            boolean isMerged = false;
                            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                                if (mergedRegion.isInRange(rowIndex, cellIndex)) {
                                    isMerged = true;
                                    break;
                                }
                            }

                            String mergedFlag = isMerged ? "(合并)" : "";
                            System.out.print("[" + (cellIndex + 1) + "]" + cellValue + mergedFlag + " ");
                        } else {
                            System.out.print("[" + (cellIndex + 1) + "]NULL ");
                        }
                    }
                    System.out.println();
                } else {
                    System.out.println("第" + (rowIndex + 1) + "行: NULL");
                }
            }

        } catch (Exception e) {
            System.out.println("读取Excel文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试模板替换功能
     */
    @Test
    public void testTemplateReplace() {
        System.out.println("开始测试模板替换功能...");

        // 创建输出目录
        createOutputDirectory();

        // 先创建一个复杂的模板
        createComplexTemplate();

        // 准备替换数据
        List<OrderMaster> orders = createComplexTestData();

        try {
            // 计算统计数据
            BigDecimal totalAmount = orders.stream()
                    .map(OrderMaster::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            int totalProducts = orders.stream()
                    .mapToInt(order -> order.getOrderDetails().size())
                    .sum();

            // 使用模板替换功能
            ExcelHelper.opsReplace()
                    .from("src/test/java/excel/export/excel/complexTemplate.xlsx")
                    .var("companyName", "金融云科技有限公司")
                    .var("reportTitle", "订单统计报表")
                    .var("reportDate", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()))
                    .var("totalOrders", orders.size())
                    .var("totalAmount", "¥" + totalAmount.toString())
                    .var("totalProducts", totalProducts)
                    .var("reportPerson", "系统管理员")
                    .loop("orders", convertToComplexMapList(orders))
                    .replaceTo("src/test/java/excel/export/excel/complexTemplateResult.xlsx");

            System.out.println("复杂模板替换完成: src/test/java/excel/export/excel/complexTemplateResult.xlsx");

            // 验证样式继承
            verifyStyleInheritance();

        } catch (Exception e) {
            System.out.println("模板替换测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 验证样式继承情况
     */
    private void verifyStyleInheritance() {
        System.out.println("\n=== 样式继承验证报告 ===");

        try (FileInputStream templateStream = new FileInputStream("src/test/java/excel/export/excel/complexTemplate.xlsx");
             FileInputStream resultStream = new FileInputStream("src/test/java/excel/export/excel/complexTemplateResult.xlsx")) {

            Workbook templateWorkbook = new XSSFWorkbook(templateStream);
            Workbook resultWorkbook = new XSSFWorkbook(resultStream);

            Sheet templateSheet = templateWorkbook.getSheetAt(0);
            Sheet resultSheet = resultWorkbook.getSheetAt(0);

            System.out.println("模板文件行数: " + (templateSheet.getLastRowNum() + 1));
            System.out.println("结果文件行数: " + (resultSheet.getLastRowNum() + 1));

            // 检查标题行样式
            checkRowStyleInheritance(templateSheet, resultSheet, 0, "标题行");
            checkRowStyleInheritance(templateSheet, resultSheet, 1, "副标题行");
            checkRowStyleInheritance(templateSheet, resultSheet, 2, "信息行");
            checkRowStyleInheritance(templateSheet, resultSheet, 5, "表头行");

            // 检查数据行样式（循环生成的行）
            System.out.println("\n--- 循环生成的数据行样式检查 ---");
            for (int i = 6; i < Math.min(resultSheet.getLastRowNum(), 15); i++) {
                Row resultRow = resultSheet.getRow(i);
                if (resultRow != null && !isLoopMarkerRow(resultRow)) {
                    checkDataRowStyle(resultRow, i);
                }
            }

            templateWorkbook.close();
            resultWorkbook.close();

            System.out.println("\n=== 样式继承验证完成 ===\n");

        } catch (Exception e) {
            System.out.println("样式验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查行样式继承
     */
    private void checkRowStyleInheritance(Sheet templateSheet, Sheet resultSheet, int rowIndex, String rowDescription) {
        Row templateRow = templateSheet.getRow(rowIndex);
        Row resultRow = resultSheet.getRow(rowIndex);

        if (templateRow == null || resultRow == null) {
            System.out.println(rowDescription + ": 行不存在，跳过检查");
            return;
        }

        System.out.println("\n--- " + rowDescription + " (第" + (rowIndex + 1) + "行) ---");
        System.out.println("模板行高: " + templateRow.getHeight() + " -> 结果行高: " + resultRow.getHeight());

        // 检查每个单元格的样式
        int lastCellNum = Math.max(templateRow.getLastCellNum(), resultRow.getLastCellNum());
        for (int cellIndex = 0; cellIndex < lastCellNum; cellIndex++) {
            Cell templateCell = templateRow.getCell(cellIndex);
            Cell resultCell = resultRow.getCell(cellIndex);

            if (templateCell != null && resultCell != null) {
                compareStyles(templateCell, resultCell, rowDescription + "-列" + (cellIndex + 1));
            }
        }
    }

    /**
     * 检查数据行样式
     */
    private void checkDataRowStyle(Row dataRow, int rowIndex) {
        System.out.println("数据行" + (rowIndex + 1) + ": ");
        for (int cellIndex = 0; cellIndex < dataRow.getLastCellNum(); cellIndex++) {
            Cell cell = dataRow.getCell(cellIndex);
            if (cell != null) {
                CellStyle style = cell.getCellStyle();
                System.out.print("  列" + (cellIndex + 1) + ": " + cell.getStringCellValue() +
                               " [对齐:" + style.getAlignment() +
                               ", 背景:" + style.getFillForegroundColor() + "]");
            }
        }
        System.out.println();
    }

    /**
     * 比较两个单元格的样式
     */
    private void compareStyles(Cell templateCell, Cell resultCell, String cellDescription) {
        CellStyle templateStyle = templateCell.getCellStyle();
        CellStyle resultStyle = resultCell.getCellStyle();

        boolean styleMatched =
            templateStyle.getAlignment() == resultStyle.getAlignment() &&
            templateStyle.getFillForegroundColor() == resultStyle.getFillForegroundColor() &&
            templateStyle.getBorderTop() == resultStyle.getBorderTop() &&
            templateStyle.getBorderBottom() == resultStyle.getBorderBottom();

        System.out.println("  " + cellDescription + ": " +
                          (styleMatched ? "✓ 样式继承正常" : "✗ 样式可能有差异"));

        if (!styleMatched) {
            System.out.println("    模板: " + templateCell.getStringCellValue() +
                             " [对齐:" + templateStyle.getAlignment() + ", 背景:" + templateStyle.getFillForegroundColor() + "]");
            System.out.println("    结果: " + resultCell.getStringCellValue() +
                             " [对齐:" + resultStyle.getAlignment() + ", 背景:" + resultStyle.getFillForegroundColor() + "]");
        }
    }

    /**
     * 判断是否是循环标记行
     */
    private boolean isLoopMarkerRow(Row row) {
        Cell firstCell = row.getCell(0);
        if (firstCell != null) {
            String cellValue = firstCell.getStringCellValue();
            return cellValue.contains("${#foreach") || cellValue.contains("${/foreach}");
        }
        return false;
    }

    /**
     * 创建简单模板
     */
    private void createSimpleTemplate() {
        try {
            // 创建一个简单的模板数据对象
            List<java.util.Map<String, Object>> templateData = new ArrayList<>();
            java.util.Map<String, Object> templateRow = new java.util.HashMap<>();
            templateRow.put("template1", "");
            templateRow.put("template2", "");
            templateRow.put("template3", "");
            templateData.add(templateRow);

            ExcelHelper.opsExport(PoiWorkbookType.XLSX)
                    .opsSheet(templateData)
                    .opsHeader()
                        .simple()
                        .texts("${companyName}", "报表日期: ${reportDate}", "订单总数: ${totalOrders}")
                        .done()
                    .opsColumn()
                        .fields("template1", "template2", "template3")
                        .done()
                    .done()
                    .export("src/test/java/excel/export/excel/template.xlsx");
        } catch (Exception e) {
            System.out.println("创建模板失败: " + e.getMessage());
        }
    }

    /**
     * 创建复杂模板
     */
    private void createComplexTemplate() {
        try {
            // 使用POI直接创建包含循环标记的模板
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("订单报表模板");

            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);           // 标题样式
            CellStyle headerStyle = createHeaderStyle(workbook);        // 表头样式
            CellStyle infoStyle = createInfoStyle(workbook);            // 信息样式
            CellStyle dataStyle = createDataStyle(workbook);            // 数据样式
            CellStyle loopMarkerStyle = createLoopMarkerStyle(workbook); // 循环标记样式
            CellStyle summaryStyle = createSummaryStyle(workbook);      // 总结样式

            int rowIndex = 0;

            // 第0行：公司名称 (A1:F1合并)
            Row row0 = sheet.createRow(rowIndex++);
            Cell companyCell = row0.createCell(0);
            companyCell.setCellValue("${companyName}");
            companyCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5)); // A1:F1
            row0.setHeight((short) 600); // 设置行高

            // 第1行：报表标题 (A2:F2合并)
            Row row1 = sheet.createRow(rowIndex++);
            Cell titleCell = row1.createCell(0);
            titleCell.setCellValue("${reportTitle}");
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 5)); // A2:F2
            row1.setHeight((short) 500); // 设置行高

            // 第2行：报表日期和制表人
            Row row2 = sheet.createRow(rowIndex++);
            Cell dateCell = row2.createCell(0);
            dateCell.setCellValue("报表日期: ${reportDate}");
            dateCell.setCellStyle(infoStyle);
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 2)); // A3:C3

            Cell personCell = row2.createCell(3);
            personCell.setCellValue("制表人: ${reportPerson}");
            personCell.setCellStyle(infoStyle);
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 3, 5)); // D3:F3

            // 第3行：统计信息
            Row row3 = sheet.createRow(rowIndex++);
            Cell totalOrdersCell = row3.createCell(0);
            totalOrdersCell.setCellValue("订单总数: ${totalOrders}");
            totalOrdersCell.setCellStyle(infoStyle);
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 1)); // A4:B4

            Cell totalProductsCell = row3.createCell(2);
            totalProductsCell.setCellValue("商品总数: ${totalProducts}");
            totalProductsCell.setCellStyle(infoStyle);
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 2, 3)); // C4:D4

            Cell totalAmountCell = row3.createCell(4);
            totalAmountCell.setCellValue("总金额: ${totalAmount}");
            totalAmountCell.setCellStyle(infoStyle);
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 4, 5)); // E4:F4

            // 第4行：空行
            sheet.createRow(rowIndex++);

            // 第5行：表头
            Row headerRow = sheet.createRow(rowIndex++);
            Cell[] headerCells = new Cell[6];
            String[] headerTexts = {"订单号", "客户名称", "订单日期", "订单金额", "订单状态", "商品数量"};
            for (int i = 0; i < headerTexts.length; i++) {
                headerCells[i] = headerRow.createCell(i);
                headerCells[i].setCellValue(headerTexts[i]);
                headerCells[i].setCellStyle(headerStyle);
            }
            headerRow.setHeight((short) 400); // 设置表头行高

            // 第6行：循环开始标记
            Row loopStartRow = sheet.createRow(rowIndex++);
            Cell loopStartCell = loopStartRow.createCell(0);
            loopStartCell.setCellValue("${#foreach orders}");
            loopStartCell.setCellStyle(loopMarkerStyle);

            // 第7行：循环体 - 订单数据行（确保所有列都有内容）
            Row dataRow = sheet.createRow(rowIndex++);
            String[] dataFields = {"${item.orderNo}", "${item.customerName}", "${item.orderDate}",
                                  "${item.totalAmount}", "${item.status}", "${item.productCount}"};
            for (int i = 0; i < dataFields.length; i++) {
                Cell dataCell = dataRow.createCell(i);
                dataCell.setCellValue(dataFields[i]);
                dataCell.setCellStyle(dataStyle);
            }

            // 第8行：循环结束标记
            Row loopEndRow = sheet.createRow(rowIndex++);
            Cell loopEndCell = loopEndRow.createCell(0);
            loopEndCell.setCellValue("${/foreach}");
            loopEndCell.setCellStyle(loopMarkerStyle);

            // 第9行：空行
            sheet.createRow(rowIndex++);

            // 第10行：总结信息
            Row summaryRow = sheet.createRow(rowIndex++);
            Cell summaryCell = summaryRow.createCell(0);
            summaryCell.setCellValue("以上为订单明细数据");
            summaryCell.setCellStyle(summaryStyle);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, 5)); // 合并A10:F10

            // 设置列宽
            sheet.setColumnWidth(0, 3000);  // 订单号
            sheet.setColumnWidth(1, 4000);  // 客户名称
            sheet.setColumnWidth(2, 3500);  // 订单日期
            sheet.setColumnWidth(3, 3000);  // 订单金额
            sheet.setColumnWidth(4, 2500);  // 订单状态
            sheet.setColumnWidth(5, 2500);  // 商品数量

            // 保存模板文件
            try (FileOutputStream fileOut = new FileOutputStream("src/test/java/excel/export/excel/complexTemplate.xlsx")) {
                workbook.write(fileOut);
            }

            workbook.close();
            System.out.println("复杂模板创建成功，包含循环标记");

        } catch (Exception e) {
            System.out.println("创建复杂模板失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 转换为Map列表（用于模板替换）
     */
    private List<java.util.Map<String, Object>> convertToMapList(List<OrderMaster> orders) {
        List<java.util.Map<String, Object>> mapList = new ArrayList<>();
        for (OrderMaster order : orders) {
            java.util.Map<String, Object> map = new java.util.HashMap<>();
            map.put("orderNo", order.getOrderNo());
            map.put("customerName", order.getCustomerName());
            map.put("orderDate", new SimpleDateFormat("yyyy-MM-dd").format(order.getOrderDate()));
            map.put("totalAmount", order.getTotalAmount().toString());
            map.put("status", order.getStatus());
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 将复杂订单数据转换为Map列表，用于复杂模板替换
     */
    private List<java.util.Map<String, Object>> convertToComplexMapList(List<OrderMaster> orders) {
        List<java.util.Map<String, Object>> mapList = new ArrayList<>();
        for (OrderMaster order : orders) {
            java.util.Map<String, Object> map = new java.util.HashMap<>();
            map.put("orderNo", order.getOrderNo());
            map.put("customerName", order.getCustomerName());
            map.put("orderDate", new SimpleDateFormat("yyyy年MM月dd日").format(order.getOrderDate()));
            map.put("totalAmount", "¥" + order.getTotalAmount().toString());
            map.put("status", order.getStatus());
            map.put("remark", order.getRemark());
            map.put("productCount", order.getOrderDetails() != null ? order.getOrderDetails().size() : 0);

            // 添加明细数据
            List<java.util.Map<String, Object>> detailMapList = new ArrayList<>();
            if (order.getOrderDetails() != null) {
                for (OrderDetail detail : order.getOrderDetails()) {
                    java.util.Map<String, Object> detailMap = new java.util.HashMap<>();
                    detailMap.put("productCode", detail.getProductCode());
                    detailMap.put("productName", detail.getProductName());
                    detailMap.put("specification", detail.getSpecification());
                    detailMap.put("quantity", detail.getQuantity());
                    detailMap.put("unitPrice", "¥" + detail.getUnitPrice().toString());
                    detailMap.put("amount", "¥" + detail.getAmount().toString());
                    detailMap.put("discount", detail.getDiscount().toString() + "%");
                    detailMap.put("actualAmount", "¥" + detail.getActualAmount().toString());
                    detailMapList.add(detailMap);
                }
            }
            map.put("details", detailMapList);
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 创建输出目录
     */
    private void createOutputDirectory() {
        File dir = new File("src/test/java/excel/export/excel");
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    /**
     * 创建测试数据
     */
    private List<OrderMaster> createTestData() {
        List<OrderMaster> orders = new ArrayList<>();

        // 订单1 - 基础订单
        OrderMaster order1 = new OrderMaster();
        order1.setOrderNo("ORD001");
        order1.setCustomerName("张三");
        order1.setOrderDate(new Date());
        order1.setTotalAmount(new BigDecimal("5100.00"));
        order1.setStatus("已确认");
        order1.setRemark("普通订单");

        List<OrderDetail> details1 = new ArrayList<>();
        details1.add(new OrderDetail("P001", "笔记本电脑", "联想ThinkPad", 1,
                new BigDecimal("5000.00"), new BigDecimal("5000.00"),
                BigDecimal.ZERO, new BigDecimal("5000.00")));
        details1.add(new OrderDetail("P002", "无线鼠标", "罗技MX", 2,
                new BigDecimal("50.00"), new BigDecimal("100.00"),
                BigDecimal.ZERO, new BigDecimal("100.00")));
        order1.setOrderDetails(details1);

        // 订单2 - 有折扣的订单
        OrderMaster order2 = new OrderMaster();
        order2.setOrderNo("ORD002");
        order2.setCustomerName("李四");
        order2.setOrderDate(new Date());
        order2.setTotalAmount(new BigDecimal("760.00"));
        order2.setStatus("待发货");
        order2.setRemark("享受会员折扣");

        List<OrderDetail> details2 = new ArrayList<>();
        details2.add(new OrderDetail("P003", "机械键盘", "Cherry青轴", 1,
                new BigDecimal("200.00"), new BigDecimal("200.00"),
                new BigDecimal("5"), new BigDecimal("190.00")));
        details2.add(new OrderDetail("P004", "4K显示器", "27英寸IPS", 1,
                new BigDecimal("600.00"), new BigDecimal("600.00"),
                new BigDecimal("5"), new BigDecimal("570.00")));
        order2.setOrderDetails(details2);

        // 订单3 - 多商品订单
        OrderMaster order3 = new OrderMaster();
        order3.setOrderNo("ORD003");
        order3.setCustomerName("王五");
        order3.setOrderDate(new Date());
        order3.setTotalAmount(new BigDecimal("2150.00"));
        order3.setStatus("已发货");
        order3.setRemark("多商品组合订单");

        List<OrderDetail> details3 = new ArrayList<>();
        details3.add(new OrderDetail("P005", "蓝牙音响", "JBL便携式", 1,
                new BigDecimal("800.00"), new BigDecimal("800.00"),
                new BigDecimal("10"), new BigDecimal("720.00")));
        details3.add(new OrderDetail("P006", "降噪耳机", "索尼WH-1000XM4", 2,
                new BigDecimal("200.00"), new BigDecimal("400.00"),
                new BigDecimal("15"), new BigDecimal("340.00")));
        details3.add(new OrderDetail("P007", "充电宝", "小米20000mAh", 3,
                new BigDecimal("120.00"), new BigDecimal("360.00"),
                new BigDecimal("8"), new BigDecimal("331.20")));
        details3.add(new OrderDetail("P008", "数据线", "Type-C快充", 5,
                new BigDecimal("25.00"), new BigDecimal("125.00"),
                BigDecimal.ZERO, new BigDecimal("125.00")));
        order3.setOrderDetails(details3);

        orders.add(order1);
        orders.add(order2);
        orders.add(order3);

        return orders;
    }

    /**
     * 订单主表实体
     */
    public static class OrderMaster {
        private String orderNo;
        private String customerName;
        private Date orderDate;
        private BigDecimal totalAmount;
        private String status;
        private String remark;  // 备注
        private List<OrderDetail> orderDetails;

        // 构造函数、getter和setter方法
        public OrderMaster() {}

        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }

        public String getCustomerName() { return customerName; }
        public void setCustomerName(String customerName) { this.customerName = customerName; }

        public Date getOrderDate() { return orderDate; }
        public void setOrderDate(Date orderDate) { this.orderDate = orderDate; }

        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }

        public List<OrderDetail> getOrderDetails() { return orderDetails; }
        public void setOrderDetails(List<OrderDetail> orderDetails) { this.orderDetails = orderDetails; }
    }

    /**
     * 订单明细实体
     */
    public static class OrderDetail {
        private String productCode;
        private String productName;
        private String specification;  // 规格型号
        private Integer quantity;
        private BigDecimal unitPrice;
        private BigDecimal amount;
        private BigDecimal discount;   // 折扣百分比
        private BigDecimal actualAmount; // 实际金额

        public OrderDetail() {}

        public OrderDetail(String productCode, String productName, Integer quantity,
                          BigDecimal unitPrice, BigDecimal amount) {
            this.productCode = productCode;
            this.productName = productName;
            this.quantity = quantity;
            this.unitPrice = unitPrice;
            this.amount = amount;
            this.specification = "标准规格";
            this.discount = BigDecimal.ZERO;
            this.actualAmount = amount;
        }

        public OrderDetail(String productCode, String productName, String specification,
                          Integer quantity, BigDecimal unitPrice, BigDecimal amount,
                          BigDecimal discount, BigDecimal actualAmount) {
            this.productCode = productCode;
            this.productName = productName;
            this.specification = specification;
            this.quantity = quantity;
            this.unitPrice = unitPrice;
            this.amount = amount;
            this.discount = discount;
            this.actualAmount = actualAmount;
        }

        // getter和setter方法
        public String getProductCode() { return productCode; }
        public void setProductCode(String productCode) { this.productCode = productCode; }

        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }

        public String getSpecification() { return specification; }
        public void setSpecification(String specification) { this.specification = specification; }

        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }

        public BigDecimal getUnitPrice() { return unitPrice; }
        public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }

        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }

        public BigDecimal getDiscount() { return discount; }
        public void setDiscount(BigDecimal discount) { this.discount = discount; }

        public BigDecimal getActualAmount() { return actualAmount; }
        public void setActualAmount(BigDecimal actualAmount) { this.actualAmount = actualAmount; }
    }

    /**
     * 包含主表信息的明细类 - 用于分离式导出
     */
    public static class DetailWithMaster {
        private String masterOrderNo;
        private String masterCustomerName;
        private String productCode;
        private String productName;
        private String specification;
        private Integer quantity;
        private BigDecimal unitPrice;
        private BigDecimal amount;
        private BigDecimal discount;
        private BigDecimal actualAmount;

        // getter和setter方法
        public String getMasterOrderNo() { return masterOrderNo; }
        public void setMasterOrderNo(String masterOrderNo) { this.masterOrderNo = masterOrderNo; }

        public String getMasterCustomerName() { return masterCustomerName; }
        public void setMasterCustomerName(String masterCustomerName) { this.masterCustomerName = masterCustomerName; }

        public String getProductCode() { return productCode; }
        public void setProductCode(String productCode) { this.productCode = productCode; }

        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }

        public String getSpecification() { return specification; }
        public void setSpecification(String specification) { this.specification = specification; }

        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }

        public BigDecimal getUnitPrice() { return unitPrice; }
        public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }

        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }

        public BigDecimal getDiscount() { return discount; }
        public void setDiscount(BigDecimal discount) { this.discount = discount; }

        public BigDecimal getActualAmount() { return actualAmount; }
        public void setActualAmount(BigDecimal actualAmount) { this.actualAmount = actualAmount; }
    }

    /**
     * 创建复杂的测试数据
     */
    private List<OrderMaster> createComplexTestData() {
        List<OrderMaster> orders = new ArrayList<>();

        // 订单1 - 大额订单，已完成
        OrderMaster order1 = new OrderMaster();
        order1.setOrderNo("ORD2024001");
        order1.setCustomerName("北京科技有限公司");
        order1.setOrderDate(new Date(System.currentTimeMillis() - 86400000L)); // 昨天
        order1.setTotalAmount(new BigDecimal("15680.50"));
        order1.setStatus("已完成");
        order1.setRemark("VIP客户，优先处理");

        List<OrderDetail> details1 = new ArrayList<>();
        details1.add(new OrderDetail("LAPTOP001", "ThinkPad X1 Carbon", "14英寸/16GB/512GB",
                2, new BigDecimal("8500.00"), new BigDecimal("17000.00"),
                new BigDecimal("5"), new BigDecimal("16150.00")));
        details1.add(new OrderDetail("MOUSE001", "罗技MX Master 3", "无线/蓝牙",
                3, new BigDecimal("650.00"), new BigDecimal("1950.00"),
                new BigDecimal("10"), new BigDecimal("1755.00")));
        details1.add(new OrderDetail("KEYBOARD001", "Cherry MX机械键盘", "青轴/RGB背光",
                2, new BigDecimal("890.00"), new BigDecimal("1780.00"),
                new BigDecimal("15"), new BigDecimal("1513.00")));
        order1.setOrderDetails(details1);

        // 订单2 - 中等订单，进行中
        OrderMaster order2 = new OrderMaster();
        order2.setOrderNo("ORD2024002");
        order2.setCustomerName("上海贸易公司");
        order2.setOrderDate(new Date());
        order2.setTotalAmount(new BigDecimal("3280.00"));
        order2.setStatus("进行中");
        order2.setRemark("需要开具增值税发票");

        List<OrderDetail> details2 = new ArrayList<>();
        details2.add(new OrderDetail("MONITOR001", "Dell 27英寸显示器", "4K/IPS面板",
                2, new BigDecimal("1800.00"), new BigDecimal("3600.00"),
                new BigDecimal("8"), new BigDecimal("3312.00")));
        details2.add(new OrderDetail("CABLE001", "HDMI数据线", "2米/4K支持",
                5, new BigDecimal("45.00"), new BigDecimal("225.00"),
                new BigDecimal("0"), new BigDecimal("225.00")));
        order2.setOrderDetails(details2);

        // 订单3 - 小额订单，已取消
        OrderMaster order3 = new OrderMaster();
        order3.setOrderNo("ORD2024003");
        order3.setCustomerName("个人用户-张先生");
        order3.setOrderDate(new Date(System.currentTimeMillis() - 172800000L)); // 前天
        order3.setTotalAmount(new BigDecimal("450.00"));
        order3.setStatus("已取消");
        order3.setRemark("客户要求退款");

        List<OrderDetail> details3 = new ArrayList<>();
        details3.add(new OrderDetail("HEADSET001", "索尼WH-1000XM4", "降噪/蓝牙",
                1, new BigDecimal("2200.00"), new BigDecimal("2200.00"),
                new BigDecimal("20"), new BigDecimal("1760.00")));
        order3.setOrderDetails(details3);

        // 订单4 - 批量订单，待发货
        OrderMaster order4 = new OrderMaster();
        order4.setOrderNo("ORD2024004");
        order4.setCustomerName("深圳电子科技");
        order4.setOrderDate(new Date(System.currentTimeMillis() + 86400000L)); // 明天
        order4.setTotalAmount(new BigDecimal("28900.00"));
        order4.setStatus("待发货");
        order4.setRemark("批量采购，需要分批发货");

        List<OrderDetail> details4 = new ArrayList<>();
        details4.add(new OrderDetail("SERVER001", "Dell PowerEdge服务器", "双CPU/64GB/2TB",
                1, new BigDecimal("25000.00"), new BigDecimal("25000.00"),
                new BigDecimal("3"), new BigDecimal("24250.00")));
        details4.add(new OrderDetail("SWITCH001", "思科24口交换机", "千兆/管理型",
                2, new BigDecimal("1800.00"), new BigDecimal("3600.00"),
                new BigDecimal("5"), new BigDecimal("3420.00")));
        details4.add(new OrderDetail("UPS001", "APC不间断电源", "1500VA/在线式",
                1, new BigDecimal("1200.00"), new BigDecimal("1200.00"),
                new BigDecimal("0"), new BigDecimal("1200.00")));
        order4.setOrderDetails(details4);

        orders.add(order1);
        orders.add(order2);
        orders.add(order3);
        orders.add(order4);

        return orders;
    }

    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 16);
        font.setBold(true);
        font.setColor(IndexedColors.DARK_BLUE.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建信息样式
     */
    private CellStyle createInfoStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 11);
        font.setBold(false);
        font.setColor(IndexedColors.BLACK.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        font.setBold(false);
        font.setColor(IndexedColors.BLACK.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建循环标记样式
     */
    private CellStyle createLoopMarkerStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("Consolas");
        font.setFontHeightInPoints((short) 9);
        font.setBold(false);
        font.setColor(IndexedColors.GREY_50_PERCENT.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return style;
    }

    /**
     * 创建总结样式
     */
    private CellStyle createSummaryStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        font.setColor(IndexedColors.DARK_GREEN.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 边框
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);

        return style;
    }
}
