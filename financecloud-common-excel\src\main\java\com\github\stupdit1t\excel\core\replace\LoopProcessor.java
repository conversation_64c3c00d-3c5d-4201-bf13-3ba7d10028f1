package com.github.stupdit1t.excel.core.replace;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Excel模板循环处理器
 * 支持在Excel模板中使用循环语法来动态生成重复行
 * 
 * 循环语法格式：
 * ${#foreach listName}
 * 循环体内容，可以使用 ${item.property} 访问当前循环项的属性
 * ${/foreach}
 * 
 * <AUTHOR>
 */
public class LoopProcessor {
    
    /**
     * 循环开始标记的正则表达式
     * 匹配格式：${#foreach listName}
     */
    private static final Pattern FOREACH_START_PATTERN = Pattern.compile("\\$\\{#foreach\\s+(\\w+)\\}");
    
    /**
     * 循环结束标记
     */
    private static final String FOREACH_END = "${/foreach}";
    
    /**
     * 循环项属性访问的正则表达式
     * 匹配格式：${item.property} 或 ${item[index].property}
     */
    private static final Pattern ITEM_PROPERTY_PATTERN = Pattern.compile("\\$\\{item(?:\\[\\d+\\])?(?:\\.\\w+)*\\}");
    
    /**
     * 处理工作表中的循环
     * 
     * @param sheet 工作表
     * @param variable 变量映射
     */
    public static void processLoops(Sheet sheet, Map<String, Object> variable) {
        List<LoopRegion> loopRegions = findLoopRegions(sheet);
        
        // 从后往前处理循环区域，避免行号变化影响
        Collections.reverse(loopRegions);
        
        for (LoopRegion loopRegion : loopRegions) {
            processLoopRegion(sheet, loopRegion, variable);
        }
    }
    
    /**
     * 查找工作表中的所有循环区域
     * 
     * @param sheet 工作表
     * @return 循环区域列表
     */
    private static List<LoopRegion> findLoopRegions(Sheet sheet) {
        List<LoopRegion> loopRegions = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        
        for (int i = 0; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            String rowContent = getRowContent(row);
            Matcher startMatcher = FOREACH_START_PATTERN.matcher(rowContent);
            
            if (startMatcher.find()) {
                String listName = startMatcher.group(1);
                int startRow = i;
                int endRow = findLoopEndRow(sheet, i + 1);
                
                if (endRow > startRow) {
                    loopRegions.add(new LoopRegion(startRow, endRow, listName));
                    i = endRow; // 跳过已处理的区域
                }
            }
        }
        
        return loopRegions;
    }
    
    /**
     * 获取行的所有单元格内容
     * 
     * @param row 行
     * @return 行内容字符串
     */
    private static String getRowContent(Row row) {
        StringBuilder content = new StringBuilder();
        short lastCellNum = row.getLastCellNum();
        
        for (short j = 0; j < lastCellNum; j++) {
            Cell cell = row.getCell(j);
            if (cell != null && cell.getCellType() == CellType.STRING) {
                content.append(cell.getStringCellValue()).append(" ");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 查找循环结束行
     * 
     * @param sheet 工作表
     * @param startSearchRow 开始搜索的行号
     * @return 循环结束行号，如果未找到返回-1
     */
    private static int findLoopEndRow(Sheet sheet, int startSearchRow) {
        int lastRowNum = sheet.getLastRowNum();
        
        for (int i = startSearchRow; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            String rowContent = getRowContent(row);
            if (rowContent.contains(FOREACH_END)) {
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * 处理单个循环区域
     * 
     * @param sheet 工作表
     * @param loopRegion 循环区域
     * @param variable 变量映射
     */
    private static void processLoopRegion(Sheet sheet, LoopRegion loopRegion, Map<String, Object> variable) {
        Object listData = variable.get(loopRegion.getListName());
        if (!(listData instanceof List)) {
            return; // 如果不是列表数据，跳过处理
        }
        
        @SuppressWarnings("unchecked")
        List<Object> dataList = (List<Object>) listData;
        
        if (dataList.isEmpty()) {
            // 如果列表为空，删除循环区域
            removeRows(sheet, loopRegion.getStartRow(), loopRegion.getEndRow());
            return;
        }
        
        // 获取模板行数据（循环体内的行，排除开始和结束标记行）
        List<RowData> templateRowsData = getTemplateRowsData(sheet, loopRegion);
        
        // 删除原始循环区域
        removeRows(sheet, loopRegion.getStartRow(), loopRegion.getEndRow());
        
        // 为每个数据项创建行
        int insertRowIndex = loopRegion.getStartRow();
        for (int i = 0; i < dataList.size(); i++) {
            Object item = dataList.get(i);
            
            for (RowData templateRowData : templateRowsData) {
                Row newRow = sheet.createRow(insertRowIndex++);
                copyRowWithData(templateRowData, newRow, item, i);
            }
        }
    }
    
    /**
     * 获取循环体内的模板行数据
     * 
     * @param sheet 工作表
     * @param loopRegion 循环区域
     * @return 模板行数据列表
     */
    private static List<RowData> getTemplateRowsData(Sheet sheet, LoopRegion loopRegion) {
        List<RowData> templateRowsData = new ArrayList<>();
        
        // 跳过开始行，获取中间的模板行，跳过结束行
        for (int i = loopRegion.getStartRow() + 1; i < loopRegion.getEndRow(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                templateRowsData.add(new RowData(row));
            }
        }
        
        return templateRowsData;
    }
    
    /**
     * 删除指定范围的行
     * 
     * @param sheet 工作表
     * @param startRow 开始行
     * @param endRow 结束行
     */
    private static void removeRows(Sheet sheet, int startRow, int endRow) {
        for (int i = endRow; i >= startRow; i--) {
            Row row = sheet.getRow(i);
            if (row != null) {
                sheet.removeRow(row);
            }
        }
        
        // 向上移动后续行
        int rowsToMove = endRow - startRow + 1;
        sheet.shiftRows(endRow + 1, sheet.getLastRowNum(), -rowsToMove);
    }
    
    /**
     * 复制行并填充数据
     * 
     * @param templateRowData 模板行数据
     * @param newRow 新行
     * @param item 当前循环项数据
     * @param index 当前循环索引
     */
    private static void copyRowWithData(RowData templateRowData, Row newRow, Object item, int index) {
        // 复制行高
        newRow.setHeight(templateRowData.getRowHeight());
        
        for (CellData cellData : templateRowData.getCellDataList()) {
            Cell newCell = newRow.createCell(cellData.getColumnIndex());
            
            // 先处理单元格内容
            if (cellData.getCellType() == CellType.STRING) {
                String cellValue = cellData.getStringValue();
                cellValue = replaceItemProperties(cellValue, item, index);
                newCell.setCellValue(cellValue);
            } else {
                // 复制其他类型的单元格值
                setCellValue(newCell, cellData);
            }
            
            // 最后设置样式，确保样式不被覆盖
            newCell.setCellStyle(cellData.getCellStyle());
        }
    }
    
    /**
     * 替换单元格内容中的循环项属性
     * 
     * @param cellValue 单元格值
     * @param item 当前循环项
     * @param index 当前循环索引
     * @return 替换后的值
     */
    private static String replaceItemProperties(String cellValue, Object item, int index) {
        if (StringUtils.isBlank(cellValue) || !cellValue.contains("${item")) {
            return cellValue;
        }
        
        Matcher matcher = ITEM_PROPERTY_PATTERN.matcher(cellValue);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String placeholder = matcher.group();
            Object value = getItemPropertyValue(placeholder, item, index);
            matcher.appendReplacement(result, String.valueOf(value));
        }
        
        matcher.appendTail(result);
        return result.toString();
    }
    
    /**
     * 获取循环项属性值
     * 
     * @param placeholder 占位符，如 ${item.name} 或 ${item[0].name}
     * @param item 当前循环项
     * @param index 当前循环索引
     * @return 属性值
     */
    private static Object getItemPropertyValue(String placeholder, Object item, int index) {
        try {
            // 移除 ${} 包装
            String expression = placeholder.substring(2, placeholder.length() - 1);
            
            if ("item".equals(expression)) {
                return item;
            }
            
            if (expression.startsWith("item.")) {
                String propertyPath = expression.substring(5); // 移除 "item."
                return getPropertyValue(item, propertyPath);
            }
            
            if (expression.contains("[index]")) {
                return index;
            }
            
            return placeholder; // 如果无法解析，返回原始占位符
        } catch (Exception e) {
            return placeholder;
        }
    }
    
    /**
     * 通过反射获取对象属性值
     * 
     * @param obj 对象
     * @param propertyPath 属性路径，支持嵌套如 "user.name"
     * @return 属性值
     */
    private static Object getPropertyValue(Object obj, String propertyPath) {
        if (obj == null || StringUtils.isBlank(propertyPath)) {
            return null;
        }
        
        try {
            String[] properties = propertyPath.split("\\.");
            Object current = obj;
            
            for (String property : properties) {
                if (current == null) {
                    return null;
                }
                
                if (current instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> map = (Map<String, Object>) current;
                    current = map.get(property);
                } else {
                    // 使用反射获取属性值
                    current = getFieldValue(current, property);
                }
            }
            
            return current;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 通过反射获取字段值
     * 
     * @param obj 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private static Object getFieldValue(Object obj, String fieldName) {
        try {
            Class<?> clazz = obj.getClass();
            
            // 首先尝试getter方法
            String getterName = "get" + StringUtils.capitalize(fieldName);
            try {
                return clazz.getMethod(getterName).invoke(obj);
            } catch (NoSuchMethodException e) {
                // 如果没有getter方法，尝试直接访问字段
                try {
                    java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (NoSuchFieldException ex) {
                    // 尝试父类字段
                    Class<?> superClass = clazz.getSuperclass();
                    while (superClass != null) {
                        try {
                            java.lang.reflect.Field field = superClass.getDeclaredField(fieldName);
                            field.setAccessible(true);
                            return field.get(obj);
                        } catch (NoSuchFieldException ignored) {
                            superClass = superClass.getSuperclass();
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        
        return null;
    }
    
    /**
     * 复制单元格值
     * 
     * @param sourceCell 源单元格
     * @param targetCell 目标单元格
     */
    private static void copyCellValue(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(sourceCell)) {
                    targetCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                // 其他类型保持空白
                break;
        }
    }
    
    /**
     * 行数据类，用于保存行的所有信息
     */
    private static class RowData {
        private final short rowHeight;
        private final List<CellData> cellDataList;
        
        public RowData(Row row) {
            this.rowHeight = row.getHeight();
            this.cellDataList = new ArrayList<>();
            
            short lastCellNum = row.getLastCellNum();
            for (short j = 0; j < lastCellNum; j++) {
                Cell cell = row.getCell(j);
                if (cell != null) {
                    cellDataList.add(new CellData(cell, j));
                }
            }
        }
        
        public short getRowHeight() {
            return rowHeight;
        }
        
        public List<CellData> getCellDataList() {
            return cellDataList;
        }
    }
    
    /**
     * 单元格数据类，用于保存单元格的所有信息
     */
    private static class CellData {
        private final short columnIndex;
        private final CellType cellType;
        private final CellStyle cellStyle;
        private final Object value;
        
        public CellData(Cell cell, short columnIndex) {
            this.columnIndex = columnIndex;
            this.cellType = cell.getCellType();
            this.cellStyle = cell.getCellStyle();
            this.value = getCellValue(cell);
        }
        
        private Object getCellValue(Cell cell) {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue();
                    } else {
                        return cell.getNumericCellValue();
                    }
                case STRING:
                    return cell.getStringCellValue();
                case BOOLEAN:
                    return cell.getBooleanCellValue();
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return null;
            }
        }
        
        public short getColumnIndex() {
            return columnIndex;
        }
        
        public CellType getCellType() {
            return cellType;
        }
        
        public CellStyle getCellStyle() {
            return cellStyle;
        }
        
        public String getStringValue() {
            return value != null ? value.toString() : "";
        }
        
        public Object getValue() {
            return value;
        }
    }
    
    /**
     * 设置单元格值
     * 
     * @param cell 单元格
     * @param cellData 单元格数据
     */
    private static void setCellValue(Cell cell, CellData cellData) {
        Object value = cellData.getValue();
        if (value == null) {
            return;
        }
        
        switch (cellData.getCellType()) {
            case NUMERIC:
                if (value instanceof Date) {
                    cell.setCellValue((Date) value);
                } else {
                    cell.setCellValue((Double) value);
                }
                break;
            case STRING:
                cell.setCellValue((String) value);
                break;
            case BOOLEAN:
                cell.setCellValue((Boolean) value);
                break;
            case FORMULA:
                cell.setCellFormula((String) value);
                break;
            default:
                break;
        }
    }
    
    /**
     * 循环区域信息
     */
    private static class LoopRegion {
        private final int startRow;
        private final int endRow;
        private final String listName;
        
        public LoopRegion(int startRow, int endRow, String listName) {
            this.startRow = startRow;
            this.endRow = endRow;
            this.listName = listName;
        }
        
        public int getStartRow() {
            return startRow;
        }
        
        public int getEndRow() {
            return endRow;
        }
        
        public String getListName() {
            return listName;
        }
    }
}