package com.lg.proplan.engine;

import com.lg.proplan.entity.ProductionPlan;
import com.lg.proplan.entity.ProductionPlanItem;
import com.lg.mrp.entity.MrpRequirement;
import com.lg.mrp.enums.MrpPriorityEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.math.BigDecimal;

/**
 * 生产计划单生成引擎
 */
@Slf4j
@Component
public class ProductionPlanGenerateEngine {

    /**
     * 从MRP需求生成生产计划单
     */
    public ProductionPlan generateFromMrpRequirements(String mrpPlanId, List<MrpRequirement> requirements) {
        log.info("开始从MRP需求生成生产计划单，MRP计划ID: {}, 需求数量: {}", mrpPlanId, requirements.size());
        
        if (requirements == null || requirements.isEmpty()) {
            log.warn("MRP需求列表为空，无法生成生产计划单");
            return null;
        }
        
        // 创建生产计划单
        ProductionPlan plan = new ProductionPlan();
        plan.setId(UUID.randomUUID().toString());
        plan.setPlanNo("PP" + System.currentTimeMillis());
        plan.setPlanName("从MRP需求生成的生产计划单");
        plan.setPlanDate(LocalDate.now());
        plan.setStatus("DRAFT"); // 草稿状态
        plan.setRemark("从MRP计划[" + mrpPlanId + "]生成");
        
        // 计算计划开始和结束日期
        LocalDate earliestDate = requirements.stream()
            .map(MrpRequirement::getRequirementDate)
            .min(LocalDate::compareTo)
            .orElse(LocalDate.now());
        LocalDate latestDate = requirements.stream()
            .map(MrpRequirement::getRequirementDate)
            .max(LocalDate::compareTo)
            .orElse(LocalDate.now().plusDays(30));
            
        plan.setPlanStartDate(earliestDate);
        plan.setPlanEndDate(latestDate);
        
        log.info("成功生成生产计划单，计划编号: {}, 需求数量: {}", plan.getPlanNo(), requirements.size());
        return plan;
    }

    /**
     * 计算优先级
     */
    private String calculatePriority(MrpRequirement requirement) {
        // 根据需求日期的紧急程度计算优先级
        LocalDate requirementDate = requirement.getRequirementDate();
        LocalDate today = LocalDate.now();
        
        if (requirementDate == null) {
            return MrpPriorityEnum.MEDIUM.getCode();
        }
        
        long daysUntilRequired = java.time.temporal.ChronoUnit.DAYS.between(today, requirementDate);
        
        if (daysUntilRequired <= 3) {
            return MrpPriorityEnum.HIGH.getCode(); // 3天内需要，高优先级
        } else if (daysUntilRequired <= 7) {
            return MrpPriorityEnum.MEDIUM.getCode(); // 7天内需要，中优先级
        } else {
            return MrpPriorityEnum.LOW.getCode(); // 7天后需要，低优先级
        }
    }

    /**
     * 计算计划日期
     */
    private void calculatePlanDates(ProductionPlanItem item) {
        // TODO: 实现计划日期计算逻辑
        // 可以根据物料交期、产能情况、工作日历等计算
        LocalDate today = LocalDate.now();
        item.setPlanStartDate(today);
        item.setPlanEndDate(today.plusDays(7)); // 默认7天后完成
    }

    /**
     * 创建生产计划明细项
     */
    private ProductionPlanItem createPlanItemFromRequirement(MrpRequirement requirement) {
        ProductionPlanItem item = new ProductionPlanItem();
        item.setId(UUID.randomUUID().toString());
        item.setMaterialId(requirement.getMaterialId());
        item.setMaterialCode(requirement.getMaterialCode());
        item.setMaterialName(requirement.getMaterialName());
        item.setMaterialSpec(requirement.getMaterialSpec());
        item.setUnit(requirement.getUnit());
        item.setUnitId(requirement.getUnitId());
        item.setQuantity(requirement.getNetRequirement());
        item.setWorkCenterId(requirement.getWorkCenterId());
        item.setWorkCenterName(requirement.getWorkCenterName());
        
        // 设置工程方案信息
        item.setEngineeringSolutionId(requirement.getEngineeringSolutionId());
        item.setEngineeringSolutionName(requirement.getEngineeringSolutionName());
        
        // 计算优先级
        item.setPriority(calculatePriority(requirement));
        
        // 计算计划日期
        calculatePlanDates(item, requirement);
        
        return item;
    }
    
    /**
     * 计算计划日期（重载方法）
     */
    private void calculatePlanDates(ProductionPlanItem item, MrpRequirement requirement) {
        LocalDate requirementDate = requirement.getRequirementDate();
        if (requirementDate == null) {
            requirementDate = LocalDate.now().plusDays(7);
        }
        
        // 计划开始日期：需求日期前推3天作为生产开始时间
        LocalDate planStartDate = requirementDate.minusDays(3);
        if (planStartDate.isBefore(LocalDate.now())) {
            planStartDate = LocalDate.now();
        }
        
        item.setPlanStartDate(planStartDate);
        item.setPlanEndDate(requirementDate.minusDays(1)); // 需求日期前一天完成
    }
}
